// 发票和银行回单tab的表格字段和筛选schema定义
import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { Button } from 'ant-design-vue';

// 公司选项接口
export interface CompanyOption {
  label: string;
  value: string;
}

// 表格列配置类型
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  resizable?: boolean;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  ellipsis?: boolean | { showTitle?: boolean };
  customRender?: (params: any) => any;
}

// 注意：对于简单文本列，我们现在使用 Ant Design Vue 内置的 ellipsis 功能
// 只有需要特殊处理的列（如状态、金额等）才使用自定义渲染函数



// 通用的操作按钮渲染函数
const renderActionButton = (record: any, onViewFile: (record: any) => void) => {
  return h(
    Button,
    {
      onClick: () => onViewFile(record),
      size: 'small',
      type: 'link',
    },
    () => '查看原文件',
  );
};

// 发票表格列配置
export const getInvoiceColumns = (
  activeTab: 'input' | 'output',
  onViewFile: (record: any) => void,
  displayDimension: 'invoice' | 'goods' = 'invoice',
): TableColumn[] => {
  const baseColumns: TableColumn[] = [
    {
      title: '发票号码',
      dataIndex: 'digital_invoice_number',
      key: 'digital_invoice_number',
      width: 160,
      minWidth: 120,
      maxWidth: 260,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
    },
    {
      title: displayDimension === 'goods' ? '货物或劳务名称' : '货物或劳务明细',
      dataIndex: displayDimension === 'goods' ? 'detail_goods_name' : 'goods_name',
      key: displayDimension === 'goods' ? 'detail_goods_name' : 'goods_name',
      width: 180,
      minWidth: 120,
      maxWidth: 600,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
      customRender: ({ record, text }) => {
        // 在货物维度下，如果是明细行，显示明细货物名称，否则显示原货物名称
        if (displayDimension === 'goods' && record.is_detail_row) {
          return record.detail_goods_name || text;
        }
        return text;
      },
    },
    {
      title: 'AI记账场景',
      dataIndex: displayDimension === 'goods' ? 'detail_scene' : 'scene',
      key: displayDimension === 'goods' ? 'detail_scene' : 'scene',
      width: 130,
      minWidth: 100,
      maxWidth: 200,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
      customRender: ({ record, text }) => {
        // 在货物维度下，如果是明细行，显示明细场景，否则显示原场景
        if (displayDimension === 'goods' && record.is_detail_row) {
          return record.detail_scene || record.scene || text;
        }
        return text;
      },
    },
    {
      title: '凭证号',
      dataIndex: 'voucher_num',
      key: 'voucher_num',
      width: 120,
      minWidth: 80,
      maxWidth: 180,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
    },
  ];

  // 在货物维度时添加明细相关列
  if (displayDimension === 'goods') {
    // 在凭证号列之后插入明细相关列
    const insertIndex = baseColumns.findIndex(col => col.key === 'voucher_num') + 1;

    const goodsDetailColumns: TableColumn[] = [
      {
        title: '规格型号',
        dataIndex: 'detail_specification',
        key: 'detail_specification',
        width: 120,
        minWidth: 80,
        maxWidth: 200,
        resizable: true,
        ellipsis: true,
        customRender: ({ record }) => {
          return record.is_detail_row ? (record.detail_specification || '-') : '-';
        },
      },
      {
        title: '数量',
        dataIndex: 'detail_quantity',
        key: 'detail_quantity',
        width: 100,
        minWidth: 80,
        maxWidth: 150,
        resizable: true,
        align: 'right',
        customRender: ({ record }) => {
          if (record.is_detail_row && record.detail_quantity !== undefined) {
            return record.detail_quantity.toFixed(2);
          }
          return '-';
        },
      },
      {
        title: '单价',
        dataIndex: 'detail_unit_price',
        key: 'detail_unit_price',
        width: 120,
        minWidth: 100,
        maxWidth: 180,
        resizable: true,
        align: 'right',
        customRender: ({ record }) => {
          if (record.is_detail_row && record.detail_unit_price !== undefined) {
            return record.detail_unit_price.toFixed(2);
          }
          return '-';
        },
      },
      {
        title: '单位',
        dataIndex: 'detail_unit',
        key: 'detail_unit',
        width: 80,
        minWidth: 60,
        maxWidth: 120,
        resizable: true,
        ellipsis: true,
        customRender: ({ record }) => {
          return record.is_detail_row ? (record.detail_unit || '-') : '-';
        },
      },
      {
        title: '金额',
        dataIndex: 'detail_amount',
        key: 'detail_amount',
        width: 120,
        minWidth: 100,
        maxWidth: 180,
        resizable: true,
        align: 'right',
        customRender: ({ record }) => {
          if (record.is_detail_row && record.detail_amount !== undefined) {
            return record.detail_amount.toFixed(2);
          }
          return '-';
        },
      },
    ];

    // 插入明细列
    baseColumns.splice(insertIndex, 0, ...goodsDetailColumns);
  }

  // 只在进项发票tab页显示销方名称列和税款所属期列
  if (activeTab === 'input') {
    baseColumns.push({
      title: '销方名称',
      dataIndex: 'seller_name',
      key: 'seller_name',
      width: 180,
      minWidth: 120,
      maxWidth: 250,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
    });

    // 添加税款所属期列
    baseColumns.push({
      title: '税款所属期',
      dataIndex: 'month',
      key: 'month',
      width: 120,
      minWidth: 100,
      maxWidth: 160,
      resizable: true,
      customRender: ({ text }) => {
        if (!text) return '-';
        // 将YYYYMM格式转换为YYYY年MM月显示
        const year = text.substring(0, 4);
        const month = text.substring(4, 6);
        return `${year}年${month}月`;
      },
    });
  }

  // 添加通用列
  const commonColumns: TableColumn[] = [
    {
      title: '开票日期',
      dataIndex: 'issue_date',
      key: 'issue_date',
      width: 120,
      minWidth: 100,
      maxWidth: 160,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
    },
    {
      title: '发票类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      minWidth: 80,
      maxWidth: 180,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
    },
    {
      title: '购方名称',
      dataIndex: 'buyer_name',
      key: 'buyer_name',
      width: 180,
      minWidth: 120,
      maxWidth: 250,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能
    },
    {
      title: '不含税金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      minWidth: 80,
      maxWidth: 160,
      resizable: true,
      align: 'right' as const,
      ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
      customRender: ({ text }) => {
        return text !== undefined && text !== null
          ? `¥${Number(text).toFixed(2)}`
          : '-';
      },
    },
    {
      title: '价税合计',
      dataIndex: 'total',
      key: 'total',
      width: 120,
      minWidth: 80,
      maxWidth: 160,
      resizable: true,
      align: 'right' as const,
      ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
      customRender: ({ text }) => {
        return text !== undefined && text !== null
          ? `¥${Number(text).toFixed(2)}`
          : '-';
      },
    },
    {
      title: '税额',
      dataIndex: 'total_tax',
      key: 'total_tax',
      width: 100,
      minWidth: 80,
      maxWidth: 140,
      resizable: true,
      align: 'right' as const,
      ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
      customRender: ({ text }) => {
        return text !== undefined && text !== null
          ? `¥${Number(text).toFixed(2)}`
          : '-';
      },
    },
    {
      title: '税率',
      dataIndex: 'tax_rate',
      key: 'tax_rate',
      width: 80,
      minWidth: 60,
      maxWidth: 120,
      resizable: true,
      align: 'center' as const,
      ellipsis: true, // 使用内置的省略号功能
    },
    {
      title: '发票状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      minWidth: 80,
      maxWidth: 120,
      resizable: true,
      ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
      customRender: ({ text }) => {
        // 发票状态映射：0-正常，2-已作废，3-红冲
        const statusMap: Record<string, { color: string; text: string }> = {
          '0': { color: '#52c41a', text: '正常' },
          '2': { color: '#ff4d4f', text: '已作废' },
          '3': { color: '#fa8c16', text: '红冲' },
        };

        const status = statusMap[text] || {
          color: '#666',
          text: text || '-',
        };

        return h(
          'span',
          {
            style: {
              color: status.color,
              fontWeight: '500',
            },
          },
          status.text,
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      customRender: ({ record }) => renderActionButton(record, onViewFile),
    },
  ];

  return [...baseColumns, ...commonColumns];
};

// 银行回单表格列配置
export const getBankReceiptColumns = (
  onViewFile: (record: any) => void,
): TableColumn[] => [
  {
    title: '回单编号',
    dataIndex: 'transaction_id',
    key: 'transaction_id',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '摘要',
    dataIndex: 'summary',
    key: 'summary',
    width: 200,
    minWidth: 120,
    maxWidth: 300,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: 'AI记账场景',
    dataIndex: 'scene',
    key: 'scene',
    width: 150,
    minWidth: 100,
    maxWidth: 200,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '凭证号',
    dataIndex: 'voucher_num',
    key: 'voucher_num',
    width: 120,
    minWidth: 80,
    maxWidth: 180,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '交易日期',
    dataIndex: 'transaction_time',
    key: 'transaction_time',
    width: 120,
    minWidth: 100,
    maxWidth: 180,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '收支类型',
    dataIndex: 'type',
    key: 'type',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '对方户名称',
    dataIndex: 'counterparty_account_name',
    key: 'counterparty_account_name',
    width: 180,
    minWidth: 120,
    maxWidth: 250,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '收入金额',
    dataIndex: 'income_amount',
    key: 'income_amount',
    width: 120,
    minWidth: 80,
    maxWidth: 160,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
    customRender: ({ record }) => {
      // 如果是收入类型，显示金额，否则显示 -
      const displayText = record.type === '收入' ? record.amount : '-';
      const color = record.type === '收入' ? '#52c41a' : '#999';

      return h(
        'span',
        {
          style: { color },
        },
        displayText,
      );
    },
  },
  {
    title: '支出金额',
    dataIndex: 'expense_amount',
    key: 'expense_amount',
    width: 120,
    minWidth: 80,
    maxWidth: 160,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能，会自动显示tooltip
    customRender: ({ record }) => {
      // 如果是支出类型，显示金额，否则显示 -
      const displayText = record.type === '支出' ? record.amount : '-';
      const color = record.type === '支出' ? '#ff4d4f' : '#999';

      return h(
        'span',
        {
          style: { color },
        },
        displayText,
      );
    },
  },
  {
    title: '余额',
    dataIndex: 'balance',
    key: 'balance',
    width: 120,
    minWidth: 80,
    maxWidth: 160,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '币种',
    dataIndex: 'currency',
    key: 'currency',
    width: 60,
    minWidth: 50,
    maxWidth: 80,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '备注',
    dataIndex: 'note',
    key: 'note',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '附言',
    dataIndex: 'desc',
    key: 'desc',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
    fixed: 'right' as const,
    customRender: ({ record }) => renderActionButton(record, onViewFile),
  },
];

// 工资单表格列配置
export const getPayrollColumns = (): TableColumn[] => [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '员工编号',
    dataIndex: 'employee_id',
    key: 'employee_id',
    width: 120,
    minWidth: 100,
    maxWidth: 180,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '身份证号',
    dataIndex: 'id_number',
    key: 'id_number',
    width: 160,
    minWidth: 140,
    maxWidth: 200,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '基本工资',
    dataIndex: 'basic_salary',
    key: 'basic_salary',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '津贴',
    dataIndex: 'allowances',
    key: 'allowances',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '社保扣除',
    dataIndex: 'social_insurance_deduction',
    key: 'social_insurance_deduction',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '公积金扣除',
    dataIndex: 'housing_fund_deduction',
    key: 'housing_fund_deduction',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '个人所得税',
    dataIndex: 'income_tax',
    key: 'income_tax',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '实发工资',
    dataIndex: 'net_salary',
    key: 'net_salary',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '工作天数',
    dataIndex: 'work_days',
    key: 'work_days',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    align: 'center' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '加班小时',
    dataIndex: 'overtime_hours',
    key: 'overtime_hours',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    align: 'center' as const,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '月份',
    dataIndex: 'month',
    key: 'month',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    ellipsis: true, // 使用内置的省略号功能
  },
];

// 发票列表筛选schema
export const invoiceQuerySchema = (activeTab?: string) => {
  const baseSchema: any[] = [
    // 展示维度选择框 - 放在第一位
    {
      component: 'Select',
      fieldName: 'displayDimension',
      // label: '展示维度',
      defaultValue: 'invoice', // 默认为发票维度
      componentProps: {
        options: [
          { label: '发票维度', value: 'invoice' },
          { label: '货物维度', value: 'goods' },
        ],
        style: { width: '100px' },
      },
    },
    {
      component: 'Input',
      fieldName: 'searchKeyword',
      label: '',
      componentProps: {
        placeholder: '按购方/发票号等查询',
        allowClear: true,
        style: { width: '190px' },
        suffix: '🔍',
      },
    },
  ];

  // 根据标签页类型显示不同的日期筛选器
  if (activeTab === 'input') {
    // 进项发票使用组合日期筛选器
    baseSchema.push({
      component: 'CombinedDateFilter',
      fieldName: 'combinedDateFilter',
      label: '',
      componentProps: {
        typeOptions: [
          { label: '开票日期', value: 'invoiceDate' },
          { label: '税款所属期', value: 'taxPeriod' },
        ],
        defaultValue: 'taxPeriod',
      },
    });
  } else {
    // 销项发票只显示开票日期
    baseSchema.push({
      component: 'RangePicker',
      fieldName: 'dateRange',
      label: '开票日期',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        allowClear: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '280px' },
      },
    });
  }

  baseSchema.push({
    component: 'Select',
    fieldName: 'status',
    label: '发票状态',
    defaultValue: '', // 默认选择"全部"
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '正常', value: '0' },
        { label: '已作废', value: '2' },
        { label: '红冲', value: '3' },
      ],
      allowClear: false, // 不允许清空，因为有"全部"选项
      placeholder: '请选择状态',
      style: { width: '80px' },
    },
  });

  // 添加记账状态筛选条件
  baseSchema.push({
    component: 'Select',
    fieldName: 'accounting_status',
    label: '记账状态',
    defaultValue: '', // 默认选择"全部"
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已记账', value: 'accounted' },
        { label: '未记账', value: 'unaccounted' },
      ],
      allowClear: false, // 不允许清空，因为有"全部"选项
      placeholder: '请选择记账状态',
      style: { width: '80px' },
    },
  });

  return baseSchema;
};

// 银行回单表格字段
export const bankColumns: VxeGridProps['columns'] = [
  {
    title: '公司名称',
    field: 'company_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '交易时间',
    field: 'transaction_time',
    width: 150,
  },
  {
    title: '月份',
    field: 'month',
    width: 80,
  },
  {
    title: '类型',
    field: 'type',
    width: 80,
  },
  {
    title: '凭证编号',
    field: 'voucher_num',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '交易流水号',
    field: 'transaction_id',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '账户名称',
    field: 'account_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '银行名称',
    field: 'bank_name',
    width: 120,
  },
  {
    title: '对方账户名称',
    field: 'counterparty_account_name',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '金额',
    field: 'amount',
    width: 120,
    align: 'right',
  },
  {
    title: '币种',
    field: 'currency',
    width: 60,
  },
  {
    title: '摘要',
    field: 'summary',
    width: 200,
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '备注',
    field: 'note',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 工资单表格字段
export const payrollColumns: VxeGridProps['columns'] = [
  {
    title: '姓名',
    field: 'name',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '员工编号',
    field: 'employee_id',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '身份证号',
    field: 'id_number',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '基本工资',
    field: 'basic_salary',
    width: 100,
    align: 'right',
  },
  {
    title: '津贴',
    field: 'allowances',
    width: 80,
    align: 'right',
  },
  {
    title: '社保扣除',
    field: 'social_insurance_deduction',
    width: 100,
    align: 'right',
  },
  {
    title: '公积金扣除',
    field: 'housing_fund_deduction',
    width: 100,
    align: 'right',
  },
  {
    title: '个人所得税',
    field: 'income_tax',
    width: 100,
    align: 'right',
  },
  {
    title: '实发工资',
    field: 'net_salary',
    width: 100,
    align: 'right',
  },
  {
    title: '工作天数',
    field: 'work_days',
    width: 80,
    align: 'center',
  },
  {
    title: '加班小时',
    field: 'overtime_hours',
    width: 80,
    align: 'center',
  },
  {
    title: '月份',
    field: 'month',
    width: 80,
  },
  {
    title: '备注',
    field: 'remark',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 工资单筛选schema
export const payrollQuerySchema = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '姓名',
    componentProps: {
      placeholder: '请输入姓名',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'id_number',
    label: '身份证号',
    componentProps: {
      placeholder: '请输入身份证号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'voucher_num',
    label: '凭证编号',
    componentProps: {
      placeholder: '请输入凭证编号',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'accounting_status',
    label: '记账状态',
    defaultValue: '', // 默认选择"全部"
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已记账', value: 'accounted' },
        { label: '未记账', value: 'unaccounted' },
      ],
      allowClear: false, // 不允许清空，因为有"全部"选项
      placeholder: '请选择记账状态',
      style: { width: '80px' },
    },
  },
];

// 银行回单筛选schema
export const bankQuerySchema = (bankOptions: { label: string; value: string }[] = []) => [
  {
    component: 'Select',
    fieldName: 'account_number',
    label: '', // 去掉label
    defaultValue: '', // 默认选中"全部银行"（空值）
    componentProps: {
      options: [
        { label: '全部银行', value: '' }, // 添加"全部银行"选项
        ...bankOptions
      ],
      allowClear: false, // 不允许清空，因为有"全部银行"选项
      placeholder: '请选择银行名称',
      showSearch: true,
      style: { width: '180px' }, // 增加选择框宽度以适应"后四位-银行名称"格式
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'transactionDateRange',
    label: '交易时间',
    componentProps: {
      placeholder: ['交易开始时间', '交易结束时间'],
      allowClear: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '280px' },
    },
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '收支类型',
    defaultValue: '',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '收入', value: '收入' },
        { label: '支出', value: '支出' },
      ],
      allowClear: true,
      placeholder: '请选择收支类型',
      style: { width: '80px' }, 
    },
  },
  {
    component: 'Input',
    fieldName: 'search_text',
    label: '搜索',
    componentProps: {
      placeholder: '请输入摘要/户名/备注',
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'accounting_status',
    label: '记账状态',
    defaultValue: '', // 默认选择"全部"
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已记账', value: 'accounted' },
        { label: '未记账', value: 'unaccounted' },
      ],
      allowClear: false, // 不允许清空，因为有"全部"选项
      placeholder: '请选择记账状态',
      style: { width: '80px' },
    },
  },
];
