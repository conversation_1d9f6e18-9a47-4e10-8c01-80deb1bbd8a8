<script setup lang="ts">
  import type {
    BankReceiptData,
    BankReceiptQueryParams,
    BankReceiptUpdateSceneParams,
    InvoiceData,
    InvoiceQueryParams,
    InvoiceUpdateSceneParams,
    PayrollQueryParams,
  } from '#/api/jsj-ai/types';

  import {
    computed,
    onActivated,
    onDeactivated,
    onMounted,
    onUnmounted,
    ref,
    watch,
  } from 'vue';

  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';

  import {
    updateBankReceiptScene,
    updateInvoiceScene,
  } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useColumnWidths } from '#/hooks/jsj-ai/voucher/useColumnWidths';
  import { useOriginalVoucherData } from '#/hooks/jsj-ai/voucher/useOriginalVoucherData';
  import {
    buildFileUrl as buildFileUrlUtil,
    buildInvoicePdfUrl,
  } from '#/utils/file/file-url';

  // 导入组件
  import ActionButtons from './components/ActionButtons.vue';
  import DataTable from './components/DataTable.vue';
  import FilterForm from './components/FilterForm.vue';
  import UpdateBankReceiptSceneModal from './components/UpdateBankReceiptSceneModal.vue';
  import UpdateSceneModal from './components/UpdateSceneModal.vue';
  // 导入配置和composables
  import {
    bankQuerySchema,
    getBankReceiptColumns,
    getInvoiceColumns,
    getPayrollColumns,
    invoiceQuerySchema,
    payrollQuerySchema,
  } from './data';

  defineOptions({
    name: 'VoucherOriginal',
  });

  // 标签页配置
  const tabList = [
    { key: 'output', label: '销项发票' },
    { key: 'input', label: '进项发票' },
    { key: 'bank', label: '银行回单' },
    { key: 'payroll', label: '工资单' },
  ];
  const activeTab = ref('output');

  // 使用composables
  const {
    bankSummary,
    clearSearch,
    clearSelectedInvoices,
    displayDimension,
    fetchBankReceiptData,
    fetchInvoiceData,
    fetchPayrollData,
    filterBankReceiptData,
    filterInvoiceData,
    filterPayrollData,
    handlePageSizeChange,
    handlePaginationChange,
    handleSearch,
    invoiceSummary,
    loading,
    originalBankData,
    pagination,
    rowSelection,
    // searchKeyword, // 未使用，已注释
    selectedInvoices,
    tableData,
    updateDisplayDimension,
  } = useOriginalVoucherData();

  // 根据月份选择器设置开票日期范围和税款所属期
  const setDateRangeFromMonth = () => {
    if (activeTab.value === 'input' || activeTab.value === 'output') {
      const monthValue = selectedMonth.value;
      if (monthValue) {
        const monthDate = dayjs(monthValue, 'YYYYMM');
        const begin_time = monthDate.startOf('month').format('YYYY-MM-DD');
        const end_time = monthDate.endOf('month').format('YYYY-MM-DD');
        const monthStr = monthDate.format('YYYYMM');

        // 销项发票始终设置开票日期范围
        if (activeTab.value === 'output') {
          formData.value.dateRange = [begin_time, end_time];
        }

        // 进项发票根据筛选类型设置相应的日期字段
        if (activeTab.value === 'input') {
          if (dateFilterType.value === 'taxPeriod') {
            // 税款所属期模式：设置税款所属期，不设置开票日期
            formData.value.taxMonthRange = [monthStr, monthStr];
          } else {
            // 开票日期模式：设置开票日期，不设置税款所属期
            formData.value.dateRange = [begin_time, end_time];
          }
        }
      }
    }
  };

  // 根据月份选择器设置银行回单的交易日期范围
  const setBankTransactionDateRangeFromMonth = () => {
    if (activeTab.value === 'bank') {
      const monthValue = selectedMonth.value;
      if (monthValue) {
        const monthDate = dayjs(monthValue, 'YYYYMM');
        const begin_time = monthDate.startOf('month').format('YYYY-MM-DD');
        const end_time = monthDate.endOf('month').format('YYYY-MM-DD');

        // 设置表单中的交易日期范围
        formData.value.transactionDateRange = [begin_time, end_time];
      }
    }
  };

  // 注意：开票日期和银行回单交易日期的设置已移至主要的onMounted钩子中

  const {
    handleResizeColumn,
    loadColumnWidths,
    resizeCounter,
    saveColumnWidths,
  } = useColumnWidths();

  // 自定义列宽调整处理方法
  const handleCustomResizeColumn = (w: number, col: any) => {
    handleResizeColumn(w, col, antdColumns.value, activeTab.value);
  };

  // 全局公司状态管理
  const { selectedCompany, selectedMonth } = useCompanySelection();

  // 银行选项（基于account_number去重，显示后四位+银行名称）
  const bankOptions = computed(() => {
    const bankMap = new Map<
      string,
      { account_number: string; bank_name: string }
    >();

    originalBankData.value.forEach((item) => {
      if (
        item.bank_name &&
        item.bank_name.trim() !== '' &&
        item.account_number && // 使用account_number作为唯一标识
        !bankMap.has(item.account_number)
      ) {
        bankMap.set(item.account_number, {
          account_number: item.account_number,
          bank_name: item.bank_name,
        });
      }
    });

    // 转换为选项格式，按银行名称排序
    const options = [...bankMap.entries()].map(([accountNumber, info]) => {
      // 获取账号后四位
      const lastFourDigits = accountNumber.slice(-4);
      return {
        label: `${lastFourDigits}-${info.bank_name}`,
        value: accountNumber, // 使用完整的account_number作为value
      };
    });

    // 按label排序
    return options.sort((a, b) => a.label.localeCompare(b.label));
  });

  // 表单数据
  const formData = ref<Record<string, any>>({});

  // 日期筛选类型（仅用于进项发票）
  const dateFilterType = ref<string>('taxPeriod');

  // 动态schema
  const formSchema = computed(() => {
    if (activeTab.value === 'bank') {
      return bankQuerySchema(bankOptions.value);
    } else if (activeTab.value === 'payroll') {
      return payrollQuerySchema();
    }
    return invoiceQuerySchema(activeTab.value);
  });

  // 表格列配置
  const antdColumns = ref<any[]>([]);
  const tableKey = computed(
    () => `${activeTab.value}-table-${resizeCounter.value}`,
  );

  // 更新场景相关状态
  const updateSceneModalVisible = ref(false);
  const updateBankReceiptSceneModalVisible = ref(false);
  const updateSceneLoading = ref(false);

  // PDF预览相关状态
  const pdfPreviewVisible = ref(false);
  const pdfPreviewUrl = ref('');
  const pdfPreviewTitle = ref('');
  const pdfPreviewFileType = ref<'image' | 'pdf' | 'unknown'>('unknown');

  // 生成列配置
  const updateColumns = () => {
    if (activeTab.value === 'bank') {
      antdColumns.value = getBankReceiptColumns(handleViewOriginalFile);
    } else if (activeTab.value === 'payroll') {
      antdColumns.value = getPayrollColumns();
    } else {
      antdColumns.value = getInvoiceColumns(
        activeTab.value as 'input' | 'output',
        handleViewOriginalFile,
        displayDimension.value,
      );
    }

    // 加载保存的列宽设置
    loadColumnWidths(antdColumns.value, activeTab.value);
  };

  // 重置表单数据
  const resetFormData = () => {
    const schema = formSchema.value;
    const newFormData: Record<string, any> = {};

    schema.forEach((item: any) => {
      newFormData[item.fieldName] =
        item.defaultValue === undefined ? undefined : item.defaultValue;
    });

    formData.value = newFormData;
  };

  // 查询数据的统一方法
  const fetchData = async () => {
    const values = formData.value;

    if (!selectedCompany.value) {
      message.warning('请选择公司名称');
      return;
    }

    // 从表单值中排除搜索关键字，搜索关键字只用于前端过滤
    const { searchKeyword: _, ...queryValues } = values;

    if (activeTab.value === 'bank') {
      const params: BankReceiptQueryParams = {
        company_name: selectedCompany.value,
        type: queryValues.type,
        voucher_num: queryValues.voucher_num,
      };

      // 添加交易日期筛选参数
      if (
        queryValues.transactionDateRange &&
        queryValues.transactionDateRange.length === 2
      ) {
        params.begin_time = queryValues.transactionDateRange[0];
        params.end_time = queryValues.transactionDateRange[1];
      }

      await fetchBankReceiptData(params);
      filterBankReceiptData(queryValues);
    } else if (activeTab.value === 'payroll') {
      // 使用顶部月份选择框的值
      const monthValue = selectedMonth.value;

      if (!monthValue) {
        message.warning('请先选择月份');
        return;
      }

      const params: PayrollQueryParams = {
        company_name: selectedCompany.value,
        id_number: queryValues.id_number,
        month: monthValue,
        name: queryValues.name,
        voucher_num: queryValues.voucher_num,
      };

      await fetchPayrollData(params);
      filterPayrollData(queryValues);
    } else {
      // 根据日期筛选类型决定使用哪种日期参数
      let begin_time: string | undefined;
      let end_time: string | undefined;
      let begin_month: string | undefined;
      let end_month: string | undefined;

      // 进项发票根据筛选类型处理日期参数
      if (activeTab.value === 'input' && dateFilterType.value === 'taxPeriod') {
        // 使用税款所属期
        if (
          queryValues.taxMonthRange &&
          Array.isArray(queryValues.taxMonthRange) &&
          queryValues.taxMonthRange.length === 2
        ) {
          begin_month = queryValues.taxMonthRange[0];
          end_month = queryValues.taxMonthRange[1];
        } else {
          // 如果税款所属期没有值，检查是否被用户明确清空
          // 如果没有被明确清空，则使用月份选择器的值
          const isTaxPeriodClearedByUser =
            Object.prototype.hasOwnProperty.call(
              queryValues,
              'taxMonthRange',
            ) &&
            (queryValues.taxMonthRange === null ||
              queryValues.taxMonthRange === undefined);

          if (!isTaxPeriodClearedByUser) {
            const monthValue = selectedMonth.value;
            if (monthValue) {
              const monthStr = dayjs(monthValue, 'YYYYMM').format('YYYYMM');
              begin_month = monthStr;
              end_month = monthStr;
            }
          }
        }
      } else {
        // 使用开票日期（销项发票或进项发票选择开票日期时）
        if (
          queryValues.dateRange &&
          Array.isArray(queryValues.dateRange) &&
          queryValues.dateRange.length === 2
        ) {
          begin_time = queryValues.dateRange[0];
          end_time = queryValues.dateRange[1];
        }

        // 如果用户没有输入时间范围，则使用月份选择器的值
        // 但是如果开票日期被明确清空，则不使用月份选择器的值
        if (!begin_time && !end_time) {
          // 检查是否是开票日期字段被明确清空
          // 当用户清空日期范围时，dateRange会变成null或undefined
          // 这适用于销项发票和进项发票选择开票日期模式的情况
          const isInvoiceDateClearedByUser =
            Object.prototype.hasOwnProperty.call(queryValues, 'dateRange') &&
            (queryValues.dateRange === null ||
              queryValues.dateRange === undefined);

          // 如果开票日期没有被用户明确清空，则使用月份选择器的值
          if (!isInvoiceDateClearedByUser) {
            const monthValue = selectedMonth.value;
            if (monthValue) {
              // 将YYYYMM格式转换为日期范围
              const monthDate = dayjs(monthValue, 'YYYYMM');
              begin_time = monthDate.startOf('month').format('YYYY-MM-DD');
              end_time = monthDate.endOf('month').format('YYYY-MM-DD');
            }
          }
        }
      }

      const params: InvoiceQueryParams = {
        begin_month,
        begin_time,
        company_name: selectedCompany.value,
        end_month,
        end_time,
        input_output: activeTab.value as 'input' | 'output',
        status: queryValues.status,
      };

      await fetchInvoiceData(params);
      filterInvoiceData(queryValues);
    }
  };

  // 查看原文件
  const handleViewOriginalFile = (record: any) => {
    const fileUrl = buildFileUrl(record);
    if (fileUrl === '#') {
      message.warning('该记录没有关联的原文件');
      return;
    }

    pdfPreviewUrl.value = fileUrl;
    pdfPreviewTitle.value = '原文件';
    pdfPreviewFileType.value = getFileType(fileUrl);
    pdfPreviewVisible.value = true;
  };

  // 构建文件URL
  const buildFileUrl = (row: any) => {
    // 优先使用API返回的file_url字段（发票）
    if (row.file_url) {
      return row.file_url;
    }

    // 优先使用API返回的url字段（银行回单）
    if (
      row.url &&
      (row.url.startsWith('http://') || row.url.startsWith('https://'))
    ) {
      return row.url;
    }

    const companyName = row.company_name || selectedCompany.value || '';
    if (!companyName) {
      console.warn('公司名称为空，无法构建文件URL');
      return '#';
    }

    if (row.digital_invoice_number) {
      return buildInvoicePdfUrl(
        row.digital_invoice_number,
        companyName,
        row.month,
      );
    }

    const url = row.source_file || row.url;
    if (url) {
      const isBankReceipt = activeTab.value === 'bank';
      return buildFileUrlUtil(url, companyName, isBankReceipt, row.month);
    }

    return '#';
  };

  // 检测文件类型
  const getFileType = (url: string): 'image' | 'pdf' | 'unknown' => {
    if (!url || url === '#') return 'unknown';

    // 处理带查询参数的URL
    let cleanUrl = url;
    if (url.startsWith('http://') || url.startsWith('https://')) {
      try {
        const urlObj = new URL(url);
        cleanUrl = urlObj.pathname;
      } catch {
        // 降级处理：移除查询参数
        cleanUrl = url.split('?')[0] || url;
      }
    }

    const extension = cleanUrl.split('.').pop()?.toLowerCase();
    if (
      ['bmp', 'gif', 'jpeg', 'jpg', 'png', 'webp'].includes(extension || '')
    ) {
      return 'image';
    }
    if (extension === 'pdf') {
      return 'pdf';
    }
    return 'unknown';
  };

  // 关闭PDF预览
  const closePdfPreview = () => {
    pdfPreviewVisible.value = false;
    pdfPreviewUrl.value = '';
    pdfPreviewTitle.value = '';
    pdfPreviewFileType.value = 'unknown';
  };

  // 下载文件
  const handleDownloadFile = () => {
    if (pdfPreviewUrl.value && pdfPreviewUrl.value !== '#') {
      window.open(pdfPreviewUrl.value, '_blank');
    }
  };

  // 处理字段变化
  const handleFieldChange = (fieldName: string, value: any) => {
    if (
      fieldName === 'company_name' &&
      value &&
      value !== selectedCompany.value
    ) {
      selectedCompany.value = value;
    }

    // 处理搜索关键字变化（仅对发票页面生效）
    if (
      fieldName === 'searchKeyword' &&
      (activeTab.value === 'input' || activeTab.value === 'output')
    ) {
      handleSearch(value || '');
    }

    // 处理日期筛选类型变化（仅对进项发票生效）
    if (fieldName === 'dateFilterType' && activeTab.value === 'input') {
      dateFilterType.value = value || 'invoiceDate';

      // 清空相关的日期字段
      if (value === 'taxPeriod') {
        // 切换到税款所属期，清空开票日期
        formData.value.dateRange = undefined;
      } else {
        // 切换到开票日期，清空税款所属期
        formData.value.taxMonthRange = undefined;
      }
    }

    // 处理展示维度变化（仅对发票页面生效）
    if (
      fieldName === 'displayDimension' &&
      (activeTab.value === 'input' || activeTab.value === 'output')
    ) {
      updateDisplayDimension(value || 'invoice');
      // 重新应用当前的筛选条件，确保维度切换时保持筛选状态
      const { searchKeyword: _, ...queryValues } = formData.value;
      filterInvoiceData(queryValues);
      // 更新列配置
      updateColumns();
    }
  };

  // 重置按钮点击事件
  const handleReset = () => {
    resetFormData();

    // 重置展示维度状态，确保与表单数据同步
    if (activeTab.value === 'input' || activeTab.value === 'output') {
      updateDisplayDimension('invoice'); // 重置为默认的发票维度
    }

    // 根据标签页类型设置默认日期值
    switch (activeTab.value) {
      case 'bank': {
        // 银行回单重置时设置交易日期为顶部月份值
        const monthValue = selectedMonth.value;
        if (monthValue) {
          const monthDate = dayjs(monthValue, 'YYYYMM');
          const begin_time = monthDate.startOf('month').format('YYYY-MM-DD');
          const end_time = monthDate.endOf('month').format('YYYY-MM-DD');
          formData.value.transactionDateRange = [begin_time, end_time];
        }

        break;
      }
      case 'input': {
        // 进项发票重置时保持税款所属期选择，并设置顶部月份值
        dateFilterType.value = 'taxPeriod';
        formData.value.dateFilterType = 'taxPeriod';

        // 设置税款所属期为顶部月份选择器的值
        const monthValue = selectedMonth.value;
        if (monthValue) {
          const monthStr = dayjs(monthValue, 'YYYYMM').format('YYYYMM');
          formData.value.taxMonthRange = [monthStr, monthStr];
        }

        break;
      }
      case 'output': {
        // 销项发票重置时设置开票日期为顶部月份值
        const monthValue = selectedMonth.value;
        if (monthValue) {
          const monthDate = dayjs(monthValue, 'YYYYMM');
          const begin_time = monthDate.startOf('month').format('YYYY-MM-DD');
          const end_time = monthDate.endOf('month').format('YYYY-MM-DD');
          formData.value.dateRange = [begin_time, end_time];
        }

        break;
      }
      // No default
    }

    clearSelectedInvoices();
    clearSearch(); // 清空搜索关键字
    pagination.value.current = 1;
    fetchData();
    message.success('已重置搜索条件');
  };

  // 同步数据按钮
  const handleSyncData = () => {
    let dataTypes: string[] = [];
    switch (activeTab.value) {
      case 'input': {
        dataTypes = ['一般进项', '进项专票'];
        break;
      }
      case 'output': {
        dataTypes = ['销项发票'];
        break;
      }
      default: {
        dataTypes = ['销项发票', '一般进项', '进项专票'];
        break;
      }
    }

    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'sync-data',
        dataTypes,
        message: '请同步当前公司的最新数据',
      },
    });

    window.dispatchEvent(aiToggleEvent);
  };

  // AI记账按钮
  const handleAiAccounting = () => {
    if (
      activeTab.value !== 'input' &&
      activeTab.value !== 'output' &&
      activeTab.value !== 'bank'
    ) {
      console.warn('AI记账功能仅支持发票和银行回单标签页');
      return;
    }

    let types: string[] = [];
    let message = '';

    switch (activeTab.value) {
      case 'bank': {
        types = ['银行回单'];
        message = selectedInvoices.value.length > 0 
          ? `已选择${selectedInvoices.value.length}条银行回单生成凭证`
          : '银行回单生成凭证';
        break;
      }
      case 'input': {
        types = ['进项专票', '一般进项'];
        message = selectedInvoices.value.length > 0 
          ? `已选择${selectedInvoices.value.length}条进项发票生成凭证`
          : '发票生成凭证';
        break;
      }
      case 'output': {
        types = ['销项发票'];
        message = selectedInvoices.value.length > 0 
          ? `已选择${selectedInvoices.value.length}条销项发票生成凭证`
          : '发票生成凭证';
        break;
      }
      default: {
        console.warn('未知的标签页类型:', activeTab.value);
        return;
      }
    }

    // 获取选中数据的IDs（如果有选中的话）
    const selectedIds = selectedInvoices.value.length > 0 
      ? selectedInvoices.value.map((item: any) => item._id)
      : undefined;

    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'db-to-voucher',
        message,
        types,
        ids: selectedIds, // 传递选中的IDs
      },
    });

    window.dispatchEvent(aiToggleEvent);
  };

  // 银行回单同步
  const handleBankReceiptSync = () => {
    const aiToggleEvent = new CustomEvent('ai-toggle-trigger', {
      detail: {
        action: 'bank-receipt-sync',
        message: '同步银行回单',
      },
    });

    window.dispatchEvent(aiToggleEvent);
  };

  // 更新场景按钮
  const handleUpdateScene = () => {
    if (
      activeTab.value !== 'input' &&
      activeTab.value !== 'output' &&
      activeTab.value !== 'bank'
    ) {
      message.warning('只有发票和银行回单数据支持更新场景功能');
      return;
    }

    if (selectedInvoices.value.length === 0) {
      const dataType = activeTab.value === 'bank' ? '银行回单' : '发票';
      message.warning(`请先选择要更新场景的${dataType}`);
      return;
    }

    if (activeTab.value === 'bank') {
      updateBankReceiptSceneModalVisible.value = true;
    } else {
      updateSceneModalVisible.value = true;
    }
  };

  // 批量更新发票场景
  const batchUpdateInvoiceScene = async (sceneData: any) => {
    if (selectedInvoices.value.length === 0) {
      message.warning('没有选中的发票');
      return;
    }

    updateSceneLoading.value = true;
    try {
      const updateParams: InvoiceUpdateSceneParams[] =
        selectedInvoices.value.map((invoice) => {
          // 从原始发票数据中提取现有值，如果表单中有值则优先使用表单值
          const invoiceData = invoice as InvoiceData;

          // 处理税率：将字符串转换为数字
          let taxRate = 0;
          // 只有当表单中有明确的非零税率值时才使用表单值
          if (
            sceneData.ii_tax_rates !== undefined &&
            sceneData.ii_tax_rates !== null &&
            sceneData.ii_tax_rates !== '' &&
            sceneData.ii_tax_rates !== 0
          ) {
            taxRate = Number(sceneData.ii_tax_rates);
          } else if (invoiceData.tax_rate) {
            // 从原始数据中提取税率，可能是字符串格式如"0.13"或"13%"
            const taxRateStr = String(invoiceData.tax_rate).replace('%', '');
            const parsedRate = Number(taxRateStr);
            taxRate = Number.isNaN(parsedRate) ? 0 : parsedRate;
            // 如果税率大于1，说明是百分比格式，需要转换为小数
            if (taxRate > 1) {
              taxRate = taxRate / 100;
            }
          }

          // 处理ID：如果是货物维度展开的明细行，需要提取原始发票ID
          let originalId = invoiceData._id;
          if (originalId && originalId.includes('_detail_')) {
            // 从类似 "6885da792464de8d8e1fca54_detail_1" 的ID中提取原始ID
            const extractedId = originalId.split('_detail_')[0];
            originalId = extractedId || originalId; // 确保不为undefined
          }

          // 获取detail_id（货物维度时需要）
          let detailId: number | undefined;
          if (
            displayDimension.value === 'goods' &&
            (invoiceData as any).is_detail_row
          ) {
            // 货物维度时，直接使用processedData中已经设置好的detail_id
            detailId = (invoiceData as any).detail_id;
          }

          return {
            company_name: selectedCompany.value || '',
            id: originalId,
            ii_buyer: sceneData.ii_buyer || invoiceData.buyer_name || '',
            ii_goods: sceneData.ii_goods || invoiceData.goods_name || '',
            ii_note: sceneData.ii_note || invoiceData.remark || '',
            ii_seller: sceneData.ii_seller || invoiceData.seller_name || '',
            ii_tax_rates: taxRate,
            ii_type: sceneData.ii_type || 'normal',
            scene: sceneData.scene,
            type:
              activeTab.value === 'input' ? 'input_invoice' : 'output_invoice',
            ...(detailId !== undefined && { detail_id: detailId }), // 只有在货物维度时才添加detail_id
          };
        });

      await updateInvoiceScene(updateParams);

      message.success(`成功更新 ${selectedInvoices.value.length} 条发票场景`);
      updateSceneModalVisible.value = false;
      clearSelectedInvoices();
      await fetchData();
    } catch (error: any) {
      console.error('更新发票场景失败:', error);
      message.error(error?.message || '更新发票场景失败');
    } finally {
      updateSceneLoading.value = false;
    }
  };

  // 批量更新银行回单场景
  const batchUpdateBankReceiptScene = async (sceneData: any) => {
    if (selectedInvoices.value.length === 0) {
      message.warning('没有选中的银行回单');
      return;
    }

    updateSceneLoading.value = true;
    try {
      const updateParams: BankReceiptUpdateSceneParams[] =
        selectedInvoices.value.map((item) => {
          // 类型断言为银行回单数据
          const bankReceipt = item as BankReceiptData;
          return {
            // 从银行回单原始数据获取所有字段，只有场景从表单获取
            br_account_name: bankReceipt.account_name || '',
            br_counterparty_account_name:
              bankReceipt.counterparty_account_name || '',
            br_currency: bankReceipt.currency || 'CNY',
            // br_note 是附言 + 备注的组合
            br_note:
              [bankReceipt.desc, bankReceipt.note].filter(Boolean).join(' ') ||
              '',
            br_summary: bankReceipt.summary || '',
            br_type: bankReceipt.type || '',
            company_name: selectedCompany.value || '',
            id: bankReceipt._id,
            scene: sceneData.scene,
            type: 'bank_receipt' as const,
          };
        });

      await updateBankReceiptScene(updateParams);

      message.success(
        `成功更新 ${selectedInvoices.value.length} 条银行回单场景`,
      );
      updateBankReceiptSceneModalVisible.value = false;
      clearSelectedInvoices();
      await fetchData();
    } catch (error: any) {
      console.error('更新银行回单场景失败:', error);
      message.error(error?.message || '更新银行回单场景失败');
    } finally {
      updateSceneLoading.value = false;
    }
  };

  // 处理更新场景模态框取消事件
  const handleUpdateSceneCancel = () => {
    clearSelectedInvoices();
  };

  // 监听tab切换，重置表单数据
  watch(activeTab, (newTab, oldTab) => {
    if (oldTab && antdColumns.value.length > 0) {
      saveColumnWidths(antdColumns.value, oldTab);
    }
    resetFormData();

    // 重置展示维度状态，确保与表单数据同步
    if (newTab === 'input' || newTab === 'output') {
      updateDisplayDimension('invoice'); // 重置为默认的发票维度
    }

    // 重置日期筛选类型（进项发票默认为税款所属期）
    if (newTab === 'input') {
      dateFilterType.value = 'taxPeriod';
      formData.value.dateFilterType = 'taxPeriod';

      // 设置默认的税款所属期为顶部月份选择器的值
      const monthValue = selectedMonth.value;
      if (monthValue) {
        const monthStr = dayjs(monthValue, 'YYYYMM').format('YYYYMM');
        formData.value.taxMonthRange = [monthStr, monthStr];
      }
    }

    updateColumns();
    clearSelectedInvoices();
    pagination.value.current = 1;
    // 在切换标签页时设置开票日期范围和银行回单交易日期范围
    setDateRangeFromMonth();
    setBankTransactionDateRangeFromMonth();
    fetchData();
  });

  // 监听月份变化，自动更新开票日期范围和银行回单交易日期范围
  watch(
    selectedMonth,
    () => {
      // 当月份变化时，自动更新开票日期范围（仅对发票页面生效）
      if (activeTab.value === 'input' || activeTab.value === 'output') {
        setDateRangeFromMonth();
      }
      // 当月份变化时，自动更新银行回单交易日期范围
      if (activeTab.value === 'bank') {
        setBankTransactionDateRangeFromMonth();
      }
    },
    { immediate: true },
  );

  // 监听银行回单筛选条件变化，自动应用前端筛选
  watch(
    () => [formData.value.account_number, formData.value.search_text],
    () => {
      if (activeTab.value === 'bank' && originalBankData.value.length > 0) {
        filterBankReceiptData(formData.value);
      }
    },
    { deep: true },
  );

  // 监听银行数据变化，自动设置默认选中"全部银行"
  watch(
    () => originalBankData.value,
    () => {
      if (activeTab.value === 'bank') {
        // 默认选择"全部银行"（空值）
        formData.value.account_number = '';
      }
    },
    { immediate: true },
  );

  // 页面初始化
  onMounted(async () => {
    try {
      resetFormData();

      // 初始化展示维度状态，确保与表单数据同步
      if (activeTab.value === 'input' || activeTab.value === 'output') {
        updateDisplayDimension('invoice'); // 初始化为默认的发票维度
      }

      // 初始化日期筛选类型
      if (activeTab.value === 'input') {
        dateFilterType.value = 'taxPeriod';
        formData.value.dateFilterType = 'taxPeriod';

        // 设置默认的税款所属期为顶部月份选择器的值
        const monthValue = selectedMonth.value;
        if (monthValue) {
          const monthStr = dayjs(monthValue, 'YYYYMM').format('YYYYMM');
          formData.value.taxMonthRange = [monthStr, monthStr];
        }
      }

      // 在fetchData之前设置开票日期和银行回单交易日期
      setDateRangeFromMonth();
      setBankTransactionDateRangeFromMonth();

      updateColumns();
      await fetchData();
    } catch (error) {
      console.error('页面初始化失败:', error);
      resetFormData();

      // 初始化展示维度状态，确保与表单数据同步
      if (activeTab.value === 'input' || activeTab.value === 'output') {
        updateDisplayDimension('invoice'); // 初始化为默认的发票维度
      }

      // 初始化日期筛选类型
      if (activeTab.value === 'input') {
        dateFilterType.value = 'taxPeriod';
        formData.value.dateFilterType = 'taxPeriod';

        // 设置默认的税款所属期为顶部月份选择器的值
        const monthValue = selectedMonth.value;
        if (monthValue) {
          const monthStr = dayjs(monthValue, 'YYYYMM').format('YYYYMM');
          formData.value.taxMonthRange = [monthStr, monthStr];
        }
      }

      // 在fetchData之前设置开票日期和银行回单交易日期
      setDateRangeFromMonth();
      setBankTransactionDateRangeFromMonth();

      updateColumns();
      fetchData();
    }

    // 添加数据刷新事件监听器
    const handleReloadData = () => {
      console.log('收到刷新原始凭证数据事件');
      fetchData();
    };

    window.addEventListener('reload-original-voucher-data', handleReloadData);

    // 组件卸载时移除事件监听器并保存列宽设置
    onUnmounted(() => {
      if (antdColumns.value.length > 0) {
        saveColumnWidths(antdColumns.value, activeTab.value);
      }
      window.removeEventListener(
        'reload-original-voucher-data',
        handleReloadData,
      );
    });
  });

  // keep-alive 组件激活时的处理
  onActivated(() => {
    console.log('原始凭证页面被激活 (keep-alive)');
    // 页面被激活时，可以在这里添加需要刷新的逻辑
    // 例如：检查是否需要刷新数据
  });

  // keep-alive 组件失活时的处理
  onDeactivated(() => {
    console.log('原始凭证页面被失活 (keep-alive)');
    // 页面失活时，保存当前状态
    if (antdColumns.value.length > 0) {
      saveColumnWidths(antdColumns.value, activeTab.value);
    }
  });

  // 监听展示维度变化，更新列配置
  watch(displayDimension, () => {
    updateColumns();
  });

  // 监听公司和月份变化，自动重新获取数据
  watch(
    [selectedCompany, selectedMonth],
    async ([newCompany, newMonth], [oldCompany, oldMonth]) => {
      // 只有在公司或月份真正发生变化时才重新获取数据
      if (
        (newCompany && newCompany !== oldCompany) ||
        (newMonth && newMonth !== oldMonth)
      ) {
        console.log('公司或月份发生变化，重新获取数据:', {
          newCompany,
          newMonth,
        });

        // 如果是公司变化且当前在银行回单页面，重置银行选择为"全部银行"
        if (
          newCompany &&
          newCompany !== oldCompany &&
          activeTab.value === 'bank'
        ) {
          formData.value.account_number = '';
        }

        clearSelectedInvoices();
        pagination.value.current = 1;
        await fetchData();
      }
    },
    { deep: true },
  );
</script>

<template>
  <div class="voucher-scroll-container">
    <div class="voucher-page-container">
      <!-- 整页大卡片 -->
      <a-card class="voucher-main-card">
        <!-- 头部区域：标签页 -->
        <div class="voucher-header">
          <a-tabs v-model:active-key="activeTab" class="voucher-tabs">
            <a-tab-pane
              v-for="tab in tabList"
              :key="tab.key"
              :tab="tab.label"
            />
          </a-tabs>
        </div>

        <!-- 筛选区域 -->
        <div class="voucher-filter-section">
          <!-- 搜索表单行 -->
          <div class="filter-form-row">
            <div class="filter-form-wrapper">
              <FilterForm
                :schema="formSchema"
                v-model="formData"
                @submit="fetchData"
                @field-change="handleFieldChange"
              />
            </div>
          </div>
          <!-- 操作按钮行 -->
          <div class="action-buttons-row">
            <div class="action-buttons-wrapper">
              <ActionButtons
                :active-tab="activeTab"
                :selected-count="selectedInvoices.length"
                @search="fetchData"
                @reset="handleReset"
                @sync-data="handleSyncData"
                @bank-receipt-sync="handleBankReceiptSync"
                @ai-accounting="handleAiAccounting"
                @update-scene="handleUpdateScene"
              />
            </div>
          </div>
        </div>

        <!-- 表格内容区域 -->
        <div class="voucher-table-container">
          <DataTable
            :columns="antdColumns"
            :data-source="tableData"
            :loading="loading"
            :row-selection="
              activeTab === 'input' ||
              activeTab === 'output' ||
              activeTab === 'bank'
                ? rowSelection
                : undefined
            "
            :pagination="pagination"
            :table-key="tableKey"
            :on-resize-column="handleCustomResizeColumn"
            :bank-summary="bankSummary"
            :invoice-summary="invoiceSummary"
            :active-tab="activeTab"
            @pagination-change="handlePaginationChange"
            @page-size-change="handlePageSizeChange"
          />
        </div>
      </a-card>

      <!-- 更新场景模态框 -->
      <UpdateSceneModal
        v-if="updateSceneModalVisible"
        v-model:visible="updateSceneModalVisible"
        :active-tab="activeTab"
        :loading="updateSceneLoading"
        :selected-count="selectedInvoices.length"
        :company-name="selectedCompany"
        @confirm="batchUpdateInvoiceScene"
        @cancel="handleUpdateSceneCancel"
      />

      <!-- 银行回单更新场景模态框 -->
      <UpdateBankReceiptSceneModal
        v-if="updateBankReceiptSceneModalVisible"
        v-model:visible="updateBankReceiptSceneModalVisible"
        :active-tab="activeTab"
        :loading="updateSceneLoading"
        :selected-count="selectedInvoices.length"
        :selected-bank-receipts="selectedInvoices"
        :company-name="selectedCompany"
        @confirm="batchUpdateBankReceiptScene"
        @cancel="handleUpdateSceneCancel"
      />

      <!-- 原文件预览模态框 -->
      <a-modal
        v-model:open="pdfPreviewVisible"
        :title="pdfPreviewTitle"
        width="80%"
        :footer="null"
        :centered="true"
        :destroy-on-close="true"
        @cancel="closePdfPreview"
      >
        <div class="file-preview-container">
          <!-- 图片预览 -->
          <div
            v-if="pdfPreviewFileType === 'image'"
            class="image-preview-wrapper"
          >
            <img
              :src="pdfPreviewUrl"
              :alt="pdfPreviewTitle"
              class="image-preview"
              @error="() => message.error('图片加载失败')"
            />
          </div>
          <!-- PDF预览 -->
          <iframe
            v-else-if="pdfPreviewFileType === 'pdf'"
            :src="pdfPreviewUrl"
            class="pdf-preview-iframe"
            frameborder="0"
          ></iframe>
          <!-- 未知文件类型 -->
          <div v-else class="unknown-file-preview">
            <div class="unknown-file-content">
              <a-result
                status="warning"
                title="无法预览此文件类型"
                sub-title="请下载文件后使用相应的应用程序打开"
              >
                <template #extra>
                  <a-button type="primary" @click="handleDownloadFile">
                    下载文件
                  </a-button>
                </template>
              </a-result>
            </div>
          </div>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<style scoped>
  @import './styles/index.scss';
</style>
